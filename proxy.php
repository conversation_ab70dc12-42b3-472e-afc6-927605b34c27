﻿<?php
// 设置响应头，允许来自任何源的请求（为了开发方便，后续可收紧为你的域名）
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=utf-8");

// ==================== 硬编码公告配置区域 ====================
// 方便用户修改的硬编码公告，显示在列表最上面
// 可以设置0个、1个、2个或更多个硬编码公告
// 论坛帖子数量会自动调整以达到总数5个（除非论坛帖子不足）

// 硬编码公告开关控制 - 设置为 true 启用，false 禁用
$hardcoded_announcements = [
    [
        'enabled' => true,  // 开关：true=启用，false=禁用
        'title' => '百度首页',
        'href' => 'https://www.baidu.com',
        'color' => '#FF0000', // 红色
        'tid' => 'hardcoded_1'
    ],
    [
        'enabled' => false,  // 开关：true=启用，false=禁用
        'title' => '163首页',
        'href' => 'https://www.163.com',
        'color' => '#FF8C00', // 橘色
        'tid' => 'hardcoded_2'
    ],
    [
        'enabled' => false,  // 开关：true=启用，false=禁用
        'title' => '游戏官网',
        'href' => 'https://www.example.com',
        'color' => '#27ae60', // 绿色
        'tid' => 'hardcoded_3'
    ],
    [
        'enabled' => false,  // 开关：true=启用，false=禁用
        'title' => '新手指南',
        'href' => 'https://guide.example.com',
        'color' => '#3498db', // 蓝色
        'tid' => 'hardcoded_4'
    ]
];

// 最大显示公告数量配置
$max_announcements = 5;
// ==================== 硬编码公告配置区域结束 ====================

// 目标论坛 URL - 指定版块第一页
$target_url = 'http://bbs.snmoli.com/forum.php?mod=forumdisplay&fid=2&page=1';
$base_url = 'http://bbs.snmoli.com/';

// 使用 cURL 来获取内容，这是更可靠的方式
$ch = curl_init();

// 设置 cURL 选项
curl_setopt($ch, CURLOPT_URL, $target_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 将结果作为字符串返回，而不是直接输出
curl_setopt($ch, CURLOPT_HEADER, false);        // 不包含响应头在输出中
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 跟随重定向
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'); // 模拟浏览器User-Agent
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 连接超时时间
curl_setopt($ch, CURLOPT_TIMEOUT, 20);        // 总执行超时时间
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略SSL证书验证
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 忽略SSL主机验证

// 执行 cURL 请求
$html_content = curl_exec($ch);

// 检查是否有错误发生
if(curl_errno($ch)){
    // 如果发生错误，返回一个错误信息
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '代理服务器错误: ' . curl_error($ch),
        'announcements' => []
    ]);
} else {
    // 检测编码并转换
    $detected_encoding = mb_detect_encoding($html_content, ['UTF-8', 'GBK', 'GB2312', 'BIG5'], true);

    if ($detected_encoding && $detected_encoding !== 'UTF-8') {
        $html_content = mb_convert_encoding($html_content, 'UTF-8', $detected_encoding);
    } else if (!$detected_encoding) {
        $html_content = @mb_convert_encoding($html_content, 'UTF-8', 'GBK');
    }

    // 解析帖子数据
    $posts = parseForumPosts($html_content, $base_url, $hardcoded_announcements, $max_announcements);

    // 返回JSON格式的结果，包含调试信息
    echo json_encode([
        'success' => true,
        'announcements' => $posts['posts'],
        'total' => count($posts['posts']),
        'debug_info' => [
            'html_length' => strlen($html_content),
            'encoding_detected' => mb_detect_encoding($html_content, ['UTF-8', 'GBK', 'GB2312', 'BIG5'], true),
            'contains_subject_class' => strpos($html_content, 'class="subject') !== false,
            'contains_xst_class' => strpos($html_content, 'class="s xst') !== false,
            'contains_viewthread' => strpos($html_content, 'mod=viewthread') !== false,
            'contains_sticky_section' => strpos($html_content, '全部置顶') !== false,
            'contains_digest_icon' => strpos($html_content, 'digest') !== false,
            'sticky_section_found' => $posts['debug']['sticky_section_found'],
            'normal_section_start' => $posts['debug']['normal_section_start'],
            'total_matches_found' => $posts['debug']['total_matches_found'],
            'hardcoded_count' => $posts['debug']['hardcoded_count'],
            'forum_posts_needed' => $posts['debug']['forum_posts_needed'],
            'sticky_posts_filtered' => $posts['debug']['sticky_posts_filtered'],
            'filtered_sticky_titles' => $posts['debug']['filtered_sticky_titles'],
            'normal_posts_processed' => $posts['debug']['normal_posts_processed'],
            'sample_html' => mb_substr($html_content, 0, 2000) // 前2000字符用于调试
        ]
    ]);
}

// 关闭 cURL 句柄
curl_close($ch);

// 解析论坛帖子的函数
function parseForumPosts($html_content, $base_url, $hardcoded_announcements, $max_announcements = 5) {
    $posts = [];
    // 设置正确的字符编码
    $html_content = mb_convert_encoding($html_content, 'UTF-8', 'auto');

    // 首先添加启用的硬编码公告（从配置区域读取）
    $enabled_announcements = [];
    foreach ($hardcoded_announcements as $announcement) {
        // 只添加启用的公告
        if (isset($announcement['enabled']) && $announcement['enabled'] === true) {
            // 移除 enabled 字段，只保留公告数据
            $clean_announcement = $announcement;
            unset($clean_announcement['enabled']);
            $posts[] = $clean_announcement;
            $enabled_announcements[] = $clean_announcement;
        }
    }

    // 计算需要获取的论坛帖子数量（基于实际启用的硬编码公告数量）
    $hardcoded_count = count($enabled_announcements);
    $needed_forum_posts = max(0, $max_announcements - $hardcoded_count);

    // 然后动态解析论坛帖子

    // 方法1: 查找Discuz论坛特有的帖子标题结构
    // 查找包含在th标签中且class包含"subject"的链接，确保是帖子链接
    preg_match_all('/<th[^>]*class="[^"]*subject[^"]*"[^>]*>.*?<a[^>]*href="([^"]*(?:mod=viewthread|thread-)[^"]*tid=(\d+)[^"]*)"[^>]*(?:style="([^"]*)"[^>]*)?[^>]*>([^<]+)<\/a>.*?<\/th>/is', $html_content, $matches, PREG_SET_ORDER);

    // 方法2: 如果方法1没找到，尝试查找class="s xst"的链接（这是Discuz帖子标题的标准类名）
    if (empty($matches)) {
        preg_match_all('/<a[^>]*class="[^"]*s\s+xst[^"]*"[^>]*href="([^"]*(?:mod=viewthread|thread-)[^"]*tid=(\d+)[^"]*)"[^>]*(?:style="([^"]*)"[^>]*)?[^>]*>([^<]+)<\/a>/i', $html_content, $matches, PREG_SET_ORDER);
    }

    // 方法3: 如果还是没找到，只查找明确包含mod=viewthread的链接（更安全）
    if (empty($matches)) {
        preg_match_all('/<a[^>]*href="([^"]*mod=viewthread[^"]*tid=(\d+)[^"]*)"[^>]*(?:style="([^"]*)"[^>]*)?[^>]*>([^<]+)<\/a>/i', $html_content, $matches, PREG_SET_ORDER);
    }

    $seen_tids = []; // 用于去重
    
    // 识别置顶帖子的特征模式
    $sticky_patterns = [
        // 检查是否在"全部置顶"区域
        'sticky_section' => '/<th[^>]*class="[^"]*subject[^"]*"[^>]*>.*?全部置顶.*?<\/th>/is',
        // 检查是否有digest图标
        'digest_icon' => '/digest.*?\.gif|\.png/i',
        // 检查是否有置顶相关的CSS类
        'sticky_class' => '/class="[^"]*(?:sticky|top|pin|digest|highlight|star|crown)[^"]*"/i',
        // 检查是否有置顶相关的图标
        'sticky_icon' => '/<img[^>]*(?:sticky|top|pin|digest|highlight|star|crown)[^>]*>/i'
    ];
    
    // 分析HTML结构，找出置顶帖子的位置
    $sticky_section_found = false;
    $normal_section_start = 0;
    $sticky_posts_filtered = 0; // 跟踪过滤掉的置顶帖子数量
    $filtered_sticky_titles = []; // 记录被过滤的置顶帖子标题
    $normal_posts_processed = []; // 记录处理的普通帖子标题
    
    // 查找"全部置顶"区域的结束位置
    if (preg_match('/全部置顶.*?<\/th>/is', $html_content, $sticky_match, PREG_OFFSET_CAPTURE)) {
        $sticky_section_found = true;
        // 查找分割线或普通帖子区域的开始
        $search_start = $sticky_match[0][1] + strlen($sticky_match[0][0]);
        if (preg_match('/普通主题|一般主题|<tr[^>]*class="[^"]*tr3[^"]*"|<\/tbody>.*?<tbody>/is', $html_content, $normal_match, PREG_OFFSET_CAPTURE, $search_start)) {
            $normal_section_start = $normal_match[0][1];
        }
    }

    foreach ($matches as $match) {
        $url = html_entity_decode($match[1]);
        $tid = $match[2];
        $style = isset($match[3]) ? $match[3] : '';
        $title = trim(strip_tags($match[4] ?? $match[3]));

        // 去重：如果已经处理过这个TID，跳过
        if (isset($seen_tids[$tid])) {
            continue;
        }
        $seen_tids[$tid] = true;

        // 清理标题
        $title = html_entity_decode($title);
        $title = preg_replace('/&nbsp;/', ' ', $title);
        $title = preg_replace('/\s+/', ' ', $title);
        $title = trim($title);

        // 基本验证：只过滤空标题
        if (empty($title)) {
            continue;
        }

        // 检查是否是置顶帖子
        $is_sticky = false;
        $match_position = strpos($html_content, $match[0]);
        
        // 方法1: 如果找到了"全部置顶"区域，检查帖子是否在置顶区域内
        if ($sticky_section_found && $normal_section_start > 0) {
            if ($match_position < $normal_section_start) {
                $is_sticky = true;
            }
        }
        
        // 方法2: 检查帖子HTML是否包含置顶特征
        if (!$is_sticky) {
            $full_match = $match[0];
            
            // 检查是否有digest图标
            if (preg_match('/digest.*?\.gif|digest.*?\.png/i', $full_match)) {
                $is_sticky = true;
            }
            
            // 检查是否有置顶相关的CSS类
            if (preg_match('/class="[^"]*(?:sticky|top|pin|digest|highlight|star|crown)[^"]*"/i', $full_match)) {
                $is_sticky = true;
            }
            
            // 检查是否有置顶相关的图标
            if (preg_match('/<img[^>]*(?:sticky|top|pin|digest|highlight|star|crown)[^>]*>/i', $full_match)) {
                $is_sticky = true;
            }
            
            // 检查周围的HTML内容（扩展搜索范围）
            $context_start = max(0, $match_position - 500);
            $context_end = min(strlen($html_content), $match_position + 500);
            $context = substr($html_content, $context_start, $context_end - $context_start);
            
            if (preg_match('/digest.*?\.gif|digest.*?\.png|置顶|顶置|精华|全部置顶/i', $context)) {
                $is_sticky = true;
            }
        }
        
        // 方法3: 基于标题内容判断
        if (!$is_sticky) {
            if (preg_match('/^(置顶|顶置|【置顶】|【顶置】|公告|【公告】|精华|【精华】)[\s\:\：]/u', $title)) {
                $is_sticky = true;
            }
        }

        // 如果是置顶帖子，跳过
        if ($is_sticky) {
            $sticky_posts_filtered++; // 增加过滤掉的置顶帖子计数
            $filtered_sticky_titles[] = $title; // 记录被过滤的置顶帖子标题
            continue;
        }

        // 过滤掉页面导航和系统链接，但保留所有真实的用户帖子
        // 这些是明显的论坛导航元素和页面菜单，不是用户发的帖子
        if (preg_match('/^(上一页|下一页|首页|末页|最后发表|回复|编辑|删除|举报|分享|New|new)$/u', $title)) {
            continue;
        }

        // 过滤掉页面顶部导航菜单项（从实际页面分析得出）
        if (preg_match('/^(首页|论坛|论坛BBS|赞助充值|新手指引|版主|管理员|系统|导航|菜单|登录|注册|搜索|发帖|回帖|门户|头像|充值|VIP|帮助)$/u', $title)) {
            continue;
        }

        // 过滤掉论坛功能和排序链接
        if (preg_match('/^(全部主题|最新|热门|热帖|精华|更多|发帖|站务版|投票|悬赏|活动|默认排序|发布时间|回复|查看|浏览)$/u', $title)) {
            continue;
        }

        // 过滤掉时间筛选链接
        if (preg_match('/^(全部时间|一天|两天|一周|一个月|三个月)$/u', $title)) {
            continue;
        }

        // 过滤掉其他论坛系统链接
        if (preg_match('/^(收藏本版|RSS|高级模式|Archiver|手机版|小黑屋|返回顶部|返回列表)$/u', $title)) {
            continue;
        }

        // 处理链接
        if (strpos($url, 'http') !== 0) {
            $url = $base_url . ltrim($url, '/');
        }

        // 修复可能的域名错误
        $url = str_replace('bbsftmoli.snmoli.com', 'bbs.snmoli.com', $url);

        // 提取颜色（从论坛HTML中提取真实颜色）
        $color = extractPostColor($match[0], $title, $style);

        $posts[] = [
            'title' => $title,
            'href' => $url,
            'color' => $color,
            'tid' => $tid
        ];

        // 记录处理的普通帖子标题
        $normal_posts_processed[] = $title;

        // 动态限制论坛帖子数量：硬编码公告数量 + 论坛帖子数量 = 最大显示数量
        if (count($posts) >= $max_announcements) {
            break;
        }
    }

    // 分离硬编码公告和论坛帖子
    $hardcodedPosts = array_slice($posts, 0, $hardcoded_count); // 前N个是硬编码的
    $forumPosts = array_slice($posts, $hardcoded_count); // 其余的是论坛帖子

    // 对论坛帖子按帖子ID排序（新帖子在前）
    usort($forumPosts, function($a, $b) {
        // 硬编码的帖子没有数字tid，跳过排序
        if (!is_numeric($a['tid']) || !is_numeric($b['tid'])) {
            return 0;
        }
        return intval($b['tid']) - intval($a['tid']);
    });

    // 合并结果：硬编码公告在前，论坛帖子在后，动态数量
    $finalPosts = array_merge($hardcodedPosts, array_slice($forumPosts, 0, $needed_forum_posts));

    return [
        'posts' => $finalPosts,
        'debug' => [
            'sticky_section_found' => $sticky_section_found,
            'normal_section_start' => $normal_section_start,
            'total_matches_found' => count($matches),
            'hardcoded_count' => $hardcoded_count,
            'forum_posts_needed' => $needed_forum_posts,
            'sticky_posts_filtered' => $sticky_posts_filtered, // 过滤掉的置顶帖子数量
            'filtered_sticky_titles' => $filtered_sticky_titles, // 记录被过滤的置顶帖子标题
            'normal_posts_processed' => $normal_posts_processed // 记录处理的普通帖子标题
        ]
    ];
}

// 提取帖子颜色的函数 - 从论坛HTML中提取真实颜色
function extractPostColor($linkHtml, $title = '', $style = '') {
    // 默认颜色
    $defaultColor = '#2c3e50';

    // 方法1: 从style参数中提取颜色（如果有的话）
    if (!empty($style) && preg_match('/color:\s*([^;"]+)/i', $style, $colorMatch)) {
        $color = trim($colorMatch[1]);
        if (isValidColor($color)) {
            return $color;
        }
    }

    // 方法2: 从完整的链接HTML中提取style属性的颜色
    if (preg_match('/style="[^"]*color:\s*([^;"]+)/i', $linkHtml, $colorMatch)) {
        $color = trim($colorMatch[1]);
        if (isValidColor($color)) {
            return $color;
        }
    }

    // 方法3: 查找font标签的color属性
    if (preg_match('/<font[^>]*color="([^"]*)"[^>]*>/i', $linkHtml, $colorMatch)) {
        $color = trim($colorMatch[1]);
        if (isValidColor($color)) {
            return $color;
        }
    }

    // 方法4: 根据class属性推断颜色（Discuz论坛常见的颜色类）
    if (preg_match('/class="([^"]*)"/i', $linkHtml, $classMatch)) {
        $className = strtolower($classMatch[1]);

        // Discuz论坛常见颜色类名映射
        $colorMap = [
            'red' => '#FF0000',
            'blue' => '#0000FF',
            'green' => '#008000',
            'orange' => '#FF8C00',
            'purple' => '#800080',
            'gray' => '#808080',
            'grey' => '#808080',
            'yellow' => '#FFD700',
            'pink' => '#FFC0CB',
            'brown' => '#A52A2A'
        ];

        foreach ($colorMap as $colorName => $colorValue) {
            if (strpos($className, $colorName) !== false) {
                return $colorValue;
            }
        }
    }

    // 方法5: 检查是否有特殊的论坛标记
    if (strpos($linkHtml, '置顶') !== false || strpos($linkHtml, '顶置') !== false) {
        return '#FF0000'; // 红色
    }
    if (strpos($linkHtml, '精华') !== false || strpos($linkHtml, '精品') !== false) {
        return '#FF8C00'; // 橙色
    }
    if (strpos($linkHtml, '热门') !== false) {
        return '#FF4500'; // 橙红色
    }

    // 返回默认颜色
    return $defaultColor;
}

// 验证颜色格式是否有效
function isValidColor($color) {
    // 十六进制颜色
    if (preg_match('/^#[0-9a-fA-F]{3}$/', $color) || preg_match('/^#[0-9a-fA-F]{6}$/', $color)) {
        return true;
    }

    // RGB颜色
    if (preg_match('/^rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i', $color)) {
        return true;
    }

    // 颜色名称
    $validColorNames = ['red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown', 'gray', 'grey', 'black', 'white'];
    if (in_array(strtolower($color), $validColorNames)) {
        return true;
    }

    return false;
}

?>