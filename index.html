<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Cache-Control" content="max-age=0">
<meta http-equiv="Cache-Control" content="private, no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="Thu, 01 Jan 1970 00:00:00 GMT">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<title>账号注册 - 深蓝紫主题 V3.0</title>

<!-- ==================== 配置区域 - 方便编辑 ==================== -->
<script type="text/javascript">
    // 使用自定义公告内容
    var manualAnnouncements = [
        {
            title: "游戏账号注册点这里",
            href: "http://www.snmoli.com/reg.html",
            color: "#27ae60",  // 好看的绿色
            tag: "账号注册"
        },
        {
            title: "新手攻略&服务器简介",
            href: "http://bbs.snmoli.com/forum.php?mod=viewthread&tid=114&extra=page%3D1",
            color: "#8A2BE2",  // 蓝紫色
            tag: "新手攻略"
        }
    ];

    // 论坛地址配置
    var forumBaseUrl = "http://bbs.snmoli.com/";

    // 论坛公告版块URL - 专门读取公告版块的帖子
    var forumAnnouncementUrl = "http://bbs.snmoli.com/forum.php?mod=forumdisplay&fid=2";

    // 代理文件配置 - proxy.php已经配置为读取公告版块
    var proxyUrl = 'proxy.php';
</script>
<!-- ============================================================== -->
<style type="text/css">
    /* 重置所有元素 + 性能优化 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-text-size-adjust: none;
        -ms-text-size-adjust: none;
        text-size-adjust: none;
        /* 全局性能优化 */
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    html, body {
        width: 100%;
        height: 100%;
        overflow: hidden;
        font-family: "Microsoft YaHei", "SimHei", "SimSun", sans-serif;
        background-color: transparent;
        position: relative;
        font-size: 16px;
        /* 性能优化 */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        transform: translateZ(0);
    }

    /* 背景层 */
    #background-layer {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('background.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 0;
    }

    /* 主布局容器 - 整体圆角布局 */
    #main-container {
        position: absolute;
        top: 5%;
        left: 5%;
        width: 90%;
        height: 90%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.6);
        z-index: 10;
        overflow: hidden;
        padding: 8px 15px 8px 15px;
        display: flex;
        flex-direction: column;
        /* 性能优化 */
        transform: translateZ(0);
        contain: layout style paint;
    }

    /* 内置标题样式 - 双星装饰版 */
    .content-title {
        text-align: center;
        margin: 0 0 15px 0;
        font-size: 22px;
        font-weight: 600;
        color: #2c3e50;
        letter-spacing: 4px;
        position: relative;
        padding-bottom: 8px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "PingFang SC", sans-serif;
        text-shadow: 0 1px 2px rgba(44, 62, 80, 0.1);
        opacity: 0.9;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }

    /* 标题左侧装饰图标 - 使用钻石符号 */
    .content-title::before {
        content: "◆";
        font-size: 22px;
        opacity: 0.9;
        color: #ff8a50;
        text-shadow:
            0 0 10px rgba(255, 138, 80, 0.6),
            0 0 20px rgba(255, 138, 80, 0.3),
            0 0 30px rgba(255, 138, 80, 0.1);
        transform: scale(1.4);
        animation: gentle-glow 3s ease-in-out infinite alternate;
        margin-right: 12px;
    }

    /* 标题右侧装饰图标 - 使用钻石符号 */
    .content-title::after {
        content: "◆";
        font-size: 22px;
        opacity: 0.9;
        color: #ff8a50;
        text-shadow:
            0 0 10px rgba(255, 138, 80, 0.6),
            0 0 20px rgba(255, 138, 80, 0.3),
            0 0 30px rgba(255, 138, 80, 0.1);
        transform: scale(1.4);
        animation: gentle-glow 3s ease-in-out infinite alternate;
        animation-delay: 1.5s;
        margin-left: 12px;
    }

    /* 温和的发光动画 */
    @keyframes gentle-glow {
        0% {
            text-shadow:
                0 0 10px rgba(255, 138, 80, 0.6),
                0 0 20px rgba(255, 138, 80, 0.3),
                0 0 30px rgba(255, 138, 80, 0.1);
            opacity: 0.9;
        }
        100% {
            text-shadow:
                0 0 15px rgba(255, 138, 80, 0.8),
                0 0 25px rgba(255, 138, 80, 0.4),
                0 0 35px rgba(255, 138, 80, 0.2);
            opacity: 1;
        }
    }

    /* 标题下方橘色渐变分割线 - 使用独立元素 */
    .content-title-divider {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg,
            rgba(255, 107, 53, 0) 0%,
            rgba(255, 107, 53, 0.3) 15%,
            rgba(255, 107, 53, 0.8) 50%,
            rgba(255, 107, 53, 0.3) 85%,
            rgba(255, 107, 53, 0) 100%
        );
        border-radius: 1.5px;
        pointer-events: none;
    }

    /* 内容区域 - 占据剩余空间 */
    #content {
        flex: 1;
        width: 100%;
        background: transparent;
        padding: 0;
        overflow: visible;
        min-height: 0;
    }

    /* 加载状态消息 */
    .status-message {
        text-align: center;
        font-size: 18px;
        color: #2c3e50;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        padding: 40px 20px;
    }

    /* 公告列表 - 采用固定结构 */
    .announcement-list {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 0;
        /* 防止频闪优化 */
        transform: translateZ(0);
        -webkit-perspective: 1000px;
        perspective: 1000px;
    }

    /* 公告项 - 超丝滑优化版 */
    .announcement-item {
        height: 66px; /* 增加高度 */
        width: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.92) 0%, rgba(250, 252, 255, 0.88) 100%);
        border-radius: 16px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        position: relative;
        transition: transform 0.1s linear, border-color 0.1s linear;
        border: 1px solid rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        will-change: transform;
        transform: translate3d(0, 0, 0); /* 强制3D加速 */
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        -webkit-transform-style: preserve-3d;
        transform-style: preserve-3d;
        contain: layout style paint;
    }

    /* 给公告项添加间距 - 使用margin-top */
    .announcement-item:nth-child(1) { margin-top: 0px !important; }
    .announcement-item:nth-child(2) { margin-top: 6px !important; }
    .announcement-item:nth-child(3) { margin-top: 6px !important; }
    .announcement-item:nth-child(4) { margin-top: 6px !important; }
    .announcement-item:nth-child(5) { margin-top: 6px !important; }

    /* 悬停阴影层 - 使用伪元素避免频闪 */
    .announcement-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 16px;
        box-shadow: 0 8px 24px rgba(255, 107, 53, 0.15), 0 4px 12px rgba(52, 152, 219, 0.08);
        opacity: 0;
        transition: opacity 0.1s linear;
        pointer-events: none;
        z-index: -1;
    }

    /* 悬停发光边框层 */
    .announcement-item::after {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 17px;
        background: linear-gradient(135deg, rgba(255, 160, 120, 0.3), rgba(255, 180, 140, 0.25));
        opacity: 0;
        transition: opacity 0.1s linear;
        pointer-events: none;
        z-index: -2;
    }

    /* 悬停效果 - 美化版 */
    .announcement-item:hover {
        transform: translate3d(0, -1px, 0);
        border-color: rgba(255, 138, 80, 0.6);
    }

    .announcement-item:hover::before {
        opacity: 1;
    }

    .announcement-item:hover::after {
        opacity: 1;
    }

    /* 公告标签 - 简洁设计 */
    .announcement-tag {
        position: absolute;
        left: 18px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(145deg, #ff6b35 0%, #ff8a50 40%, #ff6b35 100%);
        color: #ffffff;
        width: 88px;
        height: 38px;
        border-radius: 14px;
        font-size: 13px;
        font-weight: 700;
        display: flex;
        justify-content: center;
        align-items: center;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25), 0 2px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform, box-shadow, background;
    }

    /* 标签悬停效果 - 取消移动，只保留颜色和阴影变化 */
    .announcement-item:hover .announcement-tag {
        transform: translateY(-50%);
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25), 0 2px 6px rgba(0, 0, 0, 0.1);
        background: linear-gradient(145deg, #f0632f 0%, #ff6b35 40%, #f0632f 100%);
    }

    /* 公告序号 - 简洁设计 */
    .announcement-number {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #3498db 0%, #5dade2 50%, #2980b9 100%);
        color: white;
        border-radius: 50%;
        width: 34px;
        height: 34px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4), 0 2px 8px rgba(116, 185, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.8);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        transition: transform 0.1s linear, box-shadow 0.1s linear;
        will-change: transform;
    }

    /* 序号悬停效果 */
    .announcement-item:hover .announcement-number {
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 8px 24px rgba(52, 152, 219, 0.5), 0 4px 12px rgba(116, 185, 255, 0.4);
    }

    /* 公告链接 - 简洁设计 */
    .announcement-link {
        position: absolute;
        left: 110px; /* 从120px调整到110px，让文字更靠近标签 */
        right: 68px;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        text-decoration: none;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 12px;
        transition: color 0.1s linear;
        will-change: color;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        contain: layout style;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    .announcement-link:hover {
        color: #e67e22;
    }

    /* 固定5个公告项的高度 */
    .placeholder-row {
        height: 66px;
        visibility: hidden;
    }

    /* 最后一个元素不要下边距 */
    .announcement-list > *:last-child {
        margin-bottom: 0;
    }
</style>
</head>
<body>
    <!-- 背景层 -->
    <div id="background-layer"></div>

    <!-- 主容器 -->
    <div id="main-container">
        <!-- 内置标题 -->
        <div class="content-title">
            游戏公告
            <div class="content-title-divider"></div>
        </div>

        <!-- 内容区域 -->
        <div id="content">
            <div class="status-message">正在加载公告...</div>
        </div>
    </div>

<script type="text/javascript">
    // 显示状态消息
    function showStatus(message) {
        var contentDiv = document.getElementById('content');
        contentDiv.innerHTML = '<div class="status-message">' + message + '</div>';
    }

    // 提取颜色函数
    function extractColorFromHtml(html) {
        // 1. 查找style属性中的color（最优先）
        var styleMatch = html.match(/style="[^"]*color:\s*([^;"]+)/i);
        if (styleMatch) {
            var color = styleMatch[1].trim();
            // 验证颜色格式并转换
            if (color.match(/^#[0-9a-fA-F]{3,6}$/)) {
                return color;
            }
            if (color.match(/^rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/)) {
                return color; // 保持rgb格式
            }
            // 处理颜色名称
            var colorMap = {
                'red': '#e74c3c', 'blue': '#3498db', 'green': '#27ae60',
                'orange': '#f39c12', 'purple': '#9b59b6', 'gray': '#7f8c8d',
                'black': '#2c3e50', 'yellow': '#f1c40f'
            };
            if (colorMap[color.toLowerCase()]) {
                return colorMap[color.toLowerCase()];
            }
        }

        // 2. 查找font标签的color属性
        var fontColorMatch = html.match(/<font[^>]*color="([^"]*)"[^>]*>/i);
        if (fontColorMatch) {
            var color = fontColorMatch[1].trim();
            if (color.match(/^#[0-9a-fA-F]{3,6}$/)) {
                return color;
            }
        }

        // 3. 查找class属性，根据常见的论坛颜色类名推断颜色
        var classMatch = html.match(/class="([^"]*)"/i);
        if (classMatch) {
            var className = classMatch[1].toLowerCase();
            // Discuz论坛常见颜色类名映射
            if (className.indexOf('red') !== -1 || className.indexOf('xi1') !== -1) return '#e74c3c';
            if (className.indexOf('orange') !== -1 || className.indexOf('xi2') !== -1) return '#f39c12';
            if (className.indexOf('yellow') !== -1 || className.indexOf('xi3') !== -1) return '#f1c40f';
            if (className.indexOf('green') !== -1 || className.indexOf('xi4') !== -1) return '#27ae60';
            if (className.indexOf('blue') !== -1 || className.indexOf('xi5') !== -1) return '#3498db';
            if (className.indexOf('purple') !== -1 || className.indexOf('xi6') !== -1) return '#9b59b6';
            if (className.indexOf('gray') !== -1 || className.indexOf('grey') !== -1) return '#7f8c8d';
            if (className.indexOf('black') !== -1) return '#2c3e50';
        }

        // 4. 检查是否有特殊标记（如置顶、精华等）
        if (html.indexOf('置顶') !== -1 || html.indexOf('顶置') !== -1) return '#e74c3c';
        if (html.indexOf('精华') !== -1 || html.indexOf('精品') !== -1) return '#f39c12';
        if (html.indexOf('热门') !== -1) return '#e67e22';
        if (html.indexOf('推荐') !== -1) return '#27ae60';

        return null; // 没有找到颜色信息
    }

    // 论坛HTML解析函数
    function manualParse(html) {
        var announcements = [];

        // 专门针对Discuz论坛公告版块的解析方式
        // 方式0: 查找Discuz论坛特有的帖子列表结构 - 多种模式

        // 模式1: 查找带有subject class的th标签内的链接
        var discuzPattern1 = /<th[^>]*class="[^"]*subject[^"]*"[^>]*>[\s\S]*?<a[^>]*href="([^"]*)"[^>]*(?:style="[^"]*color:\s*([^;"]+)[^"]*"[^>]*)?[^>]*>([^<]+)<\/a>/gi;
        var match;

        while ((match = discuzPattern1.exec(html)) !== null) {
            var href = match[1].replace(/&amp;/g, '&');
            var color = match[2] ? match[2].trim() : null;
            var title = match[3].trim();

            // 清理标题
            title = title.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();

            if (title && title.length > 2 && href.indexOf('mod=viewthread') !== -1) {
                // 处理链接：如果是相对链接，添加正确的域名
                if (href.indexOf('http') !== 0) {
                    href = forumBaseUrl + href;
                } else if (href.indexOf('bbsftmoli.snmoli.com') !== -1) {
                    // 如果链接指向重定向后的域名，替换为正确的域名
                    href = href.replace('bbsftmoli.snmoli.com', 'bbs.snmoli.com');
                }

                announcements.push({
                    title: title,
                    href: href,
                    color: color || '#2c3e50'
                });
            }
        }

        // 模式2: 查找class="s xst"的链接（Discuz常用）
        if (announcements.length === 0) {
            var discuzPattern2 = /<a[^>]*class="[^"]*s\s+xst[^"]*"[^>]*href="([^"]*)"[^>]*(?:style="[^"]*color:\s*([^;"]+)[^"]*"[^>]*)?[^>]*>([^<]+)<\/a>/gi;

            while ((match = discuzPattern2.exec(html)) !== null) {
                var href = match[1].replace(/&amp;/g, '&');
                var color = match[2] ? match[2].trim() : null;
                var title = match[3].trim();

                title = title.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();

                if (title && title.length > 2 && href.indexOf('mod=viewthread') !== -1) {
                    // 处理链接：如果是相对链接，添加正确的域名
                    if (href.indexOf('http') !== 0) {
                        href = forumBaseUrl + href;
                    } else if (href.indexOf('bbsftmoli.snmoli.com') !== -1) {
                        // 如果链接指向重定向后的域名，替换为正确的域名
                        href = href.replace('bbsftmoli.snmoli.com', 'bbs.snmoli.com');
                    }

                    announcements.push({
                        title: title,
                        href: href,
                        color: color || '#2c3e50'
                    });
                }
            }
        }

        // 方式1: 查找 mod=viewthread 的链接（这是帖子的标准链接格式）
        if (announcements.length === 0) {
            var threadPattern = /<a[^>]*href="([^"]*(?:mod=viewthread|thread-\d+)[^"]*)"[^>]*[^>]*>([^<]+)<\/a>/g;

            while ((match = threadPattern.exec(html)) !== null) {
                var href = match[1].replace(/&amp;/g, '&');
                var title = match[2].trim();

                // 提取链接的完整HTML以获取颜色信息
                var linkStart = html.lastIndexOf('<a', match.index);
                var linkEnd = html.indexOf('</a>', match.index) + 4;
                var fullLinkHtml = html.substring(linkStart, linkEnd);

                // 提取颜色信息
                var color = extractColorFromHtml(fullLinkHtml);

                // 过滤掉空标题和一些无关链接
                if (title && title.length > 0 && !title.match(/^\s*$/) && !title.match(/^[\d\s\-_]+$/)) {
                    // 清理标题中的HTML标签和特殊字符
                    title = title.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();

                    // 过滤掉一些常见的非帖子链接
                    if (title.length > 2 &&
                        !title.match(/^(回复|编辑|删除|举报|分享)$/) &&
                        !title.match(/^\d+$/) &&
                        !href.match(/(action=|mod=misc|mod=space)/)) {

                        // 处理链接：如果是相对链接，添加正确的域名
                        if (href.indexOf('http') !== 0) {
                            href = forumBaseUrl + href;
                        } else if (href.indexOf('bbsftmoli.snmoli.com') !== -1) {
                            // 如果链接指向重定向后的域名，替换为正确的域名
                            href = href.replace('bbsftmoli.snmoli.com', 'bbs.snmoli.com');
                        }

                        announcements.push({
                            title: title,
                            href: href,
                            color: color || '#2c3e50' // 默认颜色
                        });
                    }
                }
            }
        }

        return announcements;
    }

    // 全新的公告显示函数 - 使用固定布局
    function displayAnnouncements(announcements) {
        var contentDiv = document.getElementById('content');

        // 始终显示5个项目，不管实际获取了多少
        var MAX_DISPLAY = 5;

        if (announcements.length > 0) {
            // 创建公告列表容器
            var listHTML = '<div class="announcement-list">';

            // 实际公告项数量
            var actualCount = Math.min(announcements.length, MAX_DISPLAY);

            // 添加实际的公告项
            for (var i = 0; i < actualCount; i++) {
                var ann = announcements[i];
                var displayTitle = ann.title;

                // 标题长度限制 - 以"2023年12月23日维护公告"为基准（15个字符）
                if (displayTitle.length > 15) {
                    displayTitle = displayTitle.substring(0, 15) + '...';
                }

                // 根据索引确定标签文字
                var tagText = "游戏公告"; // 默认标签
                if (ann.tag) {
                    tagText = ann.tag; // 如果有自定义标签，使用自定义标签
                }

                listHTML += '<div class="announcement-item">';
                listHTML += '<div class="announcement-tag">' + tagText + '</div>';
                listHTML += '<a href="' + ann.href + '" class="announcement-link" title="' + ann.title + '" target="_blank" style="color: ' + (ann.color || '#2c3e50') + ';">' + displayTitle + '</a>';
                listHTML += '<div class="announcement-number">' + (i + 1) + '</div>';
                listHTML += '</div>';
            }

            // 如果公告数量不足5个，添加占位项保持一致的布局高度
            for (var j = actualCount; j < MAX_DISPLAY; j++) {
                listHTML += '<div class="placeholder-row"></div>';
            }

            listHTML += '</div>';
            contentDiv.innerHTML = listHTML;
        } else {
            showStatus('未能找到任何公告。');
        }
    }

    // 获取公告数据
    function fetchAnnouncements() {
        // 如果有自定义公告，直接使用
        if (manualAnnouncements.length > 0) {
            displayAnnouncements(manualAnnouncements);
            return;
        }

        showStatus('正在加载公告...');
        try {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', proxyUrl + '?t=' + new Date().getTime(), true);

            // 添加强制防缓存的请求头
            xhr.setRequestHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            xhr.setRequestHeader('Pragma', 'no-cache');
            xhr.setRequestHeader('Expires', '0');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        if (!xhr.responseText || xhr.responseText.trim() === '') {
                            showStatus('错误：代理返回内容为空。');
                            return;
                        }

                        try {
                            // 尝试解析JSON响应
                            var jsonData = JSON.parse(xhr.responseText);
                            if (jsonData.success && jsonData.announcements) {
                                displayAnnouncements(jsonData.announcements);
                            } else {
                                showStatus('错误：' + (jsonData.error || '无法获取公告数据'));
                            }
                        } catch (parseError) {
                            // 如果JSON解析失败，尝试作为HTML解析（向后兼容）
                            var data = manualParse(xhr.responseText);
                            displayAnnouncements(data);
                        }
                    } else {
                        showStatus('加载失败: 服务器错误码 ' + xhr.status);
                    }
                }
            };

            xhr.onerror = function() {
                showStatus('加载失败: 无法连接到代理。');
            };

            xhr.send();
        } catch (e) {
            showStatus('发生脚本错误，公告无法加载。');
        }
    }

    // 启动程序
    window.onload = function() {
        // 获取公告数据
        fetchAnnouncements();
    };
</script>

</body>
</html>